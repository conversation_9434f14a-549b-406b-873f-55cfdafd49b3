{"name": "思盒AI", "appid": "__UNI__F0894DF", "description": "一款集先进AI技术与人性化设计于一体的创新产品", "versionName": "1.0.0", "versionCode": 1, "transformPx": false, "app-plus": {"usingComponents": true, "nvueCompiler": "uni-app", "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "nvueLaunchMode": "fast", "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"OAuth": {}, "Speech": {}, "Push": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.INTERNET\" />", "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\" />", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\" />", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\" />", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\" />", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\" />", "<uses-permission android:name=\"com.asus.msa.SupplementaryDID.ACCESS\" />", "<uses-permission android:name=\"com.huawei.android.launcher.permission.CHANGE_BADGE\" />", "<uses-permission android:name=\"android.permission.INSTALL_PACKAGES\" />", "<uses-permission android:name=\"android.permission.REQUEST_INSTALL_PACKAGES\" />"], "abiFilters": ["arm64-v8a", "x86"], "networkSecurityConfig": "static/network_security_config.xml", "usesCleartextTraffic": true}, "ios": {"UIBackgroundModes": ["audio"], "urlschemewhitelist": ["b<PERSON><PERSON><PERSON>", "iosamap"], "dSYMs": false}, "sdkConfigs": {"maps": {"amap": {"appkey_ios": "2c8a4ecb1b19c0dcbe6bc794cabb37c1", "appkey_android": "2c8a4ecb1b19c0dcbe6bc794cabb37c1"}}, "push": {"unipush": {"version": "2", "offline": false, "icons": {"small": {"ldpi": "D:/workspace/YQSJ-SVN/unpackage/dist/dev/app-plus/static/logo.png", "mdpi": "D:/workspace/YQSJ-SVN/unpackage/dist/dev/app-plus/static/logo.png", "hdpi": "D:/workspace/YQSJ-SVN/unpackage/dist/dev/app-plus/static/logo.png", "xhdpi": "D:/workspace/YQSJ-SVN/unpackage/dist/dev/app-plus/static/logo.png", "xxhdpi": "D:/workspace/YQSJ-SVN/unpackage/dist/dev/app-plus/static/logo.png"}}}}}, "orientation": ["portrait-primary"], "icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}}}, "uniStatistics": {"enable": true}}, "quickapp": {}, "quickapp-native": {"icon": "/static/logo.png", "package": "com.example.demo", "features": [{"name": "system.clipboard"}]}, "quickapp-webview": {"icon": "/static/logo.png", "package": "com.example.demo", "minPlatformVersion": 1070, "versionName": "1.0.0", "versionCode": 100}, "mp-weixin": {"appid": "wx0b02d589f1d8e728", "setting": {"urlCheck": false, "es6": true, "postcss": true, "minified": true}, "usingComponents": true, "permission": {"scope.userLocation": {"desc": "演示定位能力"}}, "uniStatistics": {"enable": true}}, "mp-alipay": {"usingComponents": true, "uniStatistics": {"enable": true}}, "mp-baidu": {"usingComponents": true, "uniStatistics": {"enable": true}, "dynamicLib": {"editorLib": {"provider": "swan-editor"}}}, "mp-toutiao": {"usingComponents": true, "uniStatistics": {"enable": true}}, "mp-jd": {"usingComponents": true, "uniStatistics": {"enable": true}}, "h5": {"template": "template.h5.html", "router": {"mode": "history", "base": "./"}, "sdkConfigs": {"maps": {"qqmap": {"key": "TKUBZ-D24AF-GJ4JY-JDVM2-IBYKK-KEBCU"}, "amap": {"key": "2c8a4ecb1b19c0dcbe6bc794cabb37c1", "securityJsCode": "", "serviceHost": ""}}}, "async": {"timeout": 20000}, "uniStatistics": {"enable": true}}, "vueVersion": "2", "mp-kuaishou": {"uniStatistics": {"enable": true}}, "mp-lark": {"uniStatistics": {"enable": true}}, "mp-qq": {"uniStatistics": {"enable": true}}, "quickapp-webview-huawei": {"uniStatistics": {"enable": true}}, "quickapp-webview-union": {"uniStatistics": {"enable": true}}, "uniStatistics": {"version": "2", "enable": true}}